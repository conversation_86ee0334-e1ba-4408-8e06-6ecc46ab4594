# TestSynth MCP Explorer

Intelligent Playwright test generation using Model Context Protocol (MCP) to actually explore websites and create meaningful tests.

## What It Does

🔍 **Explores real websites** using a browser  
🧠 **Analyzes page structure** and finds actual elements  
✨ **Generates specific tests** based on what it discovers  
🎯 **Creates working Playwright tests** you can run immediately  

## Quick Start

```bash
# Install dependencies
npm install
npm run build
npx playwright install

# Explore a website and generate tests
node scripts/generate-tests.js explore https://example.com "My Test"

# Run the generated tests
npx playwright test tests/generated/
```

## Key Features

- **Real Website Exploration**: Uses MCP to actually visit and analyze websites
- **Intelligent Test Generation**: Creates specific assertions based on discovered elements
- **Natural Language Scenarios**: Write test requirements in plain English
- **Working Output**: Generates ready-to-run Playwright tests

## Example Generated Tests

The system finds real elements and creates specific tests:

```typescript
test('Page headings are present and contain expected text', async ({ page }) => {
  await expect(page.locator('h1')).toBeVisible();
  await expect(page.locator('h1')).toContainText('Example Domain');
});

test('Links are present and accessible', async ({ page }) => {
  await expect(page.locator('a:has-text("More information...")')).toBeVisible();
});
```

## Documentation

See [INSTRUCTIONS.md](./INSTRUCTIONS.md) for detailed usage instructions.

## Important Test Examples

- `tests/generated/example-site-debug-test-3.spec.ts` - Shows MCP exploration of example.com
- `tests/generated/playwright-homepage-intelligent-test.spec.ts` - Complex real-world test
- `tests/generated/testautomation-*.spec.ts` - Scenario-based test examples

These demonstrate the MCP actually exploring websites and generating meaningful, specific tests rather than generic templates.
