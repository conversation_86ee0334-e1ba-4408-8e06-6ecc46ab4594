import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ExploreForm from '../components/ExploreForm';
import TestResults from '../components/TestResults';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const [generatedTest, setGeneratedTest] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleExploreGenerate = async (url: string, testName: string) => {
    setIsGenerating(true);
    setGeneratedTest(null);
    
    try {
      const response = await fetch('/api/explore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, testName }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate test');
      }
      
      const data = await response.json();
      setGeneratedTest(data.testContent);
    } catch (error) {
      console.error('Error generating test:', error);
      alert('Failed to generate test. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleScenarioNavigation = () => {
    navigate('/scenarios');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">
          Generate Intelligent Playwright Tests
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Explore websites with MCP to create specific, meaningful tests, or build tests from natural language scenarios.
        </p>
      </div>

      {/* Action Cards */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Generate Explore Card */}
        <div className="card">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Generate Explore</h2>
            </div>
            <p className="text-gray-600">
              Enter a website URL and let MCP explore it to generate specific tests based on actual page content.
            </p>
            <ExploreForm 
              onGenerate={handleExploreGenerate}
              isGenerating={isGenerating}
            />
          </div>
        </div>

        {/* Generate From Scenario Card */}
        <div className="card">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Generate From Scenario</h2>
            </div>
            <p className="text-gray-600">
              Write test requirements in natural language and get working Playwright tests.
            </p>
            <div className="space-y-3">
              <div className="text-sm text-gray-500">
                <strong>Example verify statements:</strong>
                <ul className="mt-1 space-y-1">
                  <li>• "Verify login button exists"</li>
                  <li>• "Verify header contains 'Welcome'"</li>
                  <li>• "Verify form accepts input"</li>
                </ul>
              </div>
              <button
                onClick={handleScenarioNavigation}
                className="btn-secondary w-full"
              >
                Build Scenario
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results Section */}
      {(generatedTest || isGenerating) && (
        <TestResults 
          testContent={generatedTest}
          isGenerating={isGenerating}
        />
      )}
    </div>
  );
};

export default Home;
