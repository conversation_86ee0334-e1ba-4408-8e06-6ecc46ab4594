"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
test_1.test.describe('Valve-Homepage-Test - Exploration Test', () => {
    test_1.test.beforeEach((_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        yield page.goto('https://www.valvesoftware.com/en/');
        yield page.waitForLoadState('networkidle');
    }));
    (0, test_1.test)('Page loads successfully with key elements', (_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Verify page loads and basic elements are present
        yield (0, test_1.expect)(page).toHaveTitle(/.+/);
        yield (0, test_1.expect)(page.locator('body')).toBeVisible();
        // Take initial screenshot
        yield page.screenshot({
            path: 'screenshots/valve-homepage-test-initial.png',
            fullPage: true
        });
    }));
    (0, test_1.test)('Navigation and interactive elements work', (_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Test navigation elements if present
        const nav = page.locator('nav, .nav, .navigation');
        if ((yield nav.count()) > 0) {
            yield (0, test_1.expect)(nav).toBeVisible();
        }
        // Test buttons if present
        const buttons = page.locator('button, input[type="button"], input[type="submit"]');
        const buttonCount = yield buttons.count();
        if (buttonCount > 0) {
            console.log(`Found ${buttonCount} interactive buttons`);
        }
        // Test forms if present
        const forms = page.locator('form');
        const formCount = yield forms.count();
        if (formCount > 0) {
            console.log(`Found ${formCount} forms`);
        }
    }));
    test_1.test.afterEach((_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Capture screenshot on failure
        if (test_1.test.info().status !== test_1.test.info().expectedStatus) {
            yield page.screenshot({
                path: `screenshots/${test_1.test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png`,
                fullPage: true
            });
        }
    }));
});
