# UI Implementation Plan

## Overview
Create a user-friendly web interface for the TestSynth MCP Explorer with two main modes:
1. **Generate Explore** - URL-based website exploration
2. **Generate From Scenario** - Scenario-based test creation

## UI Structure

### Main Page (`/`)
- **Header**: "TestSynth MCP Explorer"
- **Two main action buttons**:
  - `Generate Explore` (primary button)
  - `Generate From Scenario` (secondary button)

### Generate Explore Section
When "Generate Explore" is clicked:
- **URL Input Field**: 
  - Placeholder: "Enter website URL (e.g., https://example.com)"
  - Validation: Must be valid URL format
  - Required field
- **Test Name Input**:
  - Placeholder: "Test name (e.g., Homepage Test)"
  - Optional, auto-generates if empty
- **Generate Button**: 
  - Disabled until URL is filled
  - Shows loading state during generation
- **Results Area**: Shows generated test preview

### Generate From Scenario Page (`/scenarios`)
Navigation triggered by "Generate From Scenario" button:
- **Scenario Builder**:
  - Test Name input
  - URL input
  - **Verify Statements Section**:
    - Dynamic list of verify statement inputs
    - "Add Statement" button
    - "Remove" button for each statement
    - Placeholder examples: "Verify header is present", "Verify login button exists"
- **Generate Button**: Creates test from scenarios
- **Results Area**: Shows generated test preview

## Technical Implementation

### Frontend Framework
- **React** with TypeScript for type safety
- **Tailwind CSS** for styling
- **React Router** for navigation

### Backend API
- **Express.js** server to handle test generation
- **Endpoints**:
  - `POST /api/explore` - Website exploration
  - `POST /api/scenarios` - Scenario-based generation
  - `GET /api/tests` - List generated tests
  - `GET /api/tests/:id` - Get specific test content

### File Structure
```
ui/
├── src/
│   ├── components/
│   │   ├── ExploreForm.tsx
│   │   ├── ScenarioBuilder.tsx
│   │   ├── TestResults.tsx
│   │   └── Header.tsx
│   ├── pages/
│   │   ├── Home.tsx
│   │   └── Scenarios.tsx
│   ├── api/
│   │   └── testGeneration.ts
│   └── App.tsx
├── server/
│   ├── routes/
│   │   └── api.ts
│   └── server.ts
└── package.json
```

## User Experience Flow

### Explore Flow
1. User clicks "Generate Explore"
2. URL field appears and focuses
3. User enters URL
4. Generate button becomes enabled
5. User clicks Generate
6. Loading spinner shows
7. Results appear with generated test code
8. User can copy/download test

### Scenario Flow
1. User clicks "Generate From Scenario"
2. Navigate to scenarios page
3. User fills in test details
4. User adds verify statements
5. User clicks Generate
6. Results show generated test
7. User can copy/download test

## Implementation Steps

1. **Setup React app** with TypeScript and Tailwind
2. **Create basic layout** with header and main sections
3. **Implement ExploreForm component** with URL validation
4. **Create ScenarioBuilder component** with dynamic inputs
5. **Setup Express backend** with API endpoints
6. **Integrate with existing MCP system**
7. **Add test results display**
8. **Polish UI/UX** and add loading states

## Future Enhancements
- Test history/management
- Real-time test execution
- Test editing capabilities
- Export options (download, GitHub integration)
- Batch test generation
