import React from 'react';
import { Sc<PERSON>rio, VerifyStatement } from '../pages/Scenarios';

interface ScenarioBuilderProps {
  scenario: Scenario;
  onChange: (scenario: Scenario) => void;
  onGenerate: () => void;
  isGenerating: boolean;
  isFormValid: boolean;
}

const ScenarioBuilder: React.FC<ScenarioBuilderProps> = ({
  scenario,
  onChange,
  onGenerate,
  isGenerating,
  isFormValid
}) => {
  const handleInputChange = (field: keyof Scenario, value: string) => {
    onChange({
      ...scenario,
      [field]: value
    });
  };

  const handleVerifyStatementChange = (id: string, text: string) => {
    const updatedStatements = scenario.verifyStatements.map(vs =>
      vs.id === id ? { ...vs, text } : vs
    );
    onChange({
      ...scenario,
      verifyStatements: updatedStatements
    });
  };

  const addVerifyStatement = () => {
    const newId = (scenario.verifyStatements.length + 1).toString();
    onChange({
      ...scenario,
      verifyStatements: [...scenario.verifyStatements, { id: newId, text: '' }]
    });
  };

  const removeVerifyStatement = (id: string) => {
    if (scenario.verifyStatements.length > 1) {
      onChange({
        ...scenario,
        verifyStatements: scenario.verifyStatements.filter(vs => vs.id !== id)
      });
    }
  };

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const urlError = scenario.url && !validateUrl(scenario.url);

  return (
    <div className="space-y-6">
      <div className="grid md:grid-cols-2 gap-6">
        {/* Test Name */}
        <div>
          <label htmlFor="testName" className="block text-sm font-medium text-gray-700 mb-1">
            Test Name *
          </label>
          <input
            type="text"
            id="testName"
            value={scenario.testName}
            onChange={(e) => handleInputChange('testName', e.target.value)}
            placeholder="Login Flow Test"
            className="input-field"
            disabled={isGenerating}
          />
        </div>

        {/* URL */}
        <div>
          <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
            Website URL *
          </label>
          <input
            type="url"
            id="url"
            value={scenario.url}
            onChange={(e) => handleInputChange('url', e.target.value)}
            placeholder="https://example.com/login"
            className={`input-field ${urlError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
            disabled={isGenerating}
          />
          {urlError && (
            <p className="mt-1 text-sm text-red-600">Please enter a valid URL</p>
          )}
        </div>
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Description (optional)
        </label>
        <textarea
          id="description"
          value={scenario.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Test the user login functionality..."
          rows={3}
          className="input-field"
          disabled={isGenerating}
        />
      </div>

      {/* Verify Statements */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <label className="block text-sm font-medium text-gray-700">
            Verify Statements *
          </label>
          <button
            type="button"
            onClick={addVerifyStatement}
            disabled={isGenerating}
            className="btn-secondary text-sm"
          >
            Add Statement
          </button>
        </div>
        
        <div className="space-y-3">
          {scenario.verifyStatements.map((statement, index) => (
            <div key={statement.id} className="flex items-center space-x-3">
              <div className="flex-1">
                <input
                  type="text"
                  value={statement.text}
                  onChange={(e) => handleVerifyStatementChange(statement.id, e.target.value)}
                  placeholder={`Verify statement ${index + 1} (e.g., "Verify login button exists")`}
                  className="input-field"
                  disabled={isGenerating}
                />
              </div>
              {scenario.verifyStatements.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeVerifyStatement(statement.id)}
                  disabled={isGenerating}
                  className="text-red-600 hover:text-red-700 p-1"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              )}
            </div>
          ))}
        </div>
        
        <div className="mt-3 text-sm text-gray-500">
          <strong>Examples:</strong>
          <ul className="mt-1 space-y-1">
            <li>• "Verify header is present and contains 'Welcome'"</li>
            <li>• "Verify login button exists and is clickable"</li>
            <li>• "Verify email field accepts valid input"</li>
            <li>• "Verify form validation shows error for empty fields"</li>
          </ul>
        </div>
      </div>

      {/* Generate Button */}
      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={onGenerate}
          disabled={!isFormValid}
          className="btn-primary w-full flex items-center justify-center space-x-2"
        >
          {isGenerating ? (
            <>
              <div className="loading-spinner"></div>
              <span>Generating Test...</span>
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Generate From Scenario</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ScenarioBuilder;
