#!/usr/bin/env node

const { MCPPlaywrightClient } = require('./src/mcp-client.js');
const { PlaywrightTestGenerator } = require('./src/test-generator.js');

async function debugTestGeneration() {
  console.log('🔍 Debugging test generation...');
  
  const mcpClient = new MCPPlaywrightClient({
    command: 'npx',
    args: ['@playwright/mcp@latest', '--headless'],
    env: {}
  });

  const testGenerator = new PlaywrightTestGenerator(mcpClient, {
    outputDir: './tests/generated',
    templateDir: './test-templates',
    defaultTimeout: 30000
  });

  try {
    // Connect to MCP server
    await mcpClient.connect();
    console.log('✅ Connected to MCP server');

    // Test website exploration
    console.log('🌐 Exploring https://example.com...');
    const result = await mcpClient.capturePageInfo('https://example.com');
    
    console.log('📊 Exploration result:');
    console.log('URL:', result.url);
    console.log('Snapshot type:', typeof result.snapshot);
    console.log('Snapshot length:', result.snapshot ? result.snapshot.length : 'null');
    
    if (result.snapshot && Array.isArray(result.snapshot)) {
      console.log('📋 Snapshot content:');
      result.snapshot.forEach((item, index) => {
        console.log(`Item ${index}:`, item.text?.substring(0, 200) + '...');
      });
    }

    // Test element parsing manually
    console.log('🔍 Parsing elements manually...');
    const elements = [];

    if (Array.isArray(result.snapshot)) {
      for (const item of result.snapshot) {
        if (item.type === 'text' && item.text) {
          // Extract the YAML snapshot section
          const yamlMatch = item.text.match(/```yaml\n([\s\S]*?)\n```/);
          if (yamlMatch) {
            const yamlContent = yamlMatch[1];
            console.log('📋 YAML content:');
            console.log(yamlContent);

            // Parse YAML content
            const lines = yamlContent.split('\n');
            for (const line of lines) {
              const trimmed = line.trim();
              console.log('Processing line:', trimmed);

              if (trimmed.includes('heading')) {
                const headingMatch = trimmed.match(/heading "([^"]+)"/);
                if (headingMatch) {
                  console.log('Found heading:', headingMatch[1]);
                  elements.push({
                    type: 'heading',
                    text: headingMatch[1],
                    selector: 'h1'
                  });
                }
              } else if (trimmed.includes('link')) {
                const linkMatch = trimmed.match(/link "([^"]+)"/);
                if (linkMatch) {
                  console.log('Found link:', linkMatch[1]);
                  elements.push({
                    type: 'link',
                    text: linkMatch[1],
                    selector: `a:has-text("${linkMatch[1]}")`
                  });
                }
              }
            }
          }
        }
      }
    }

    console.log('📋 Parsed elements:');
    elements.forEach((element, index) => {
      console.log(`Element ${index}:`, element);
    });

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    // Cleanup
    await mcpClient.disconnect();
    console.log('🔌 Disconnected from MCP server');
  }
}

// Run the debug function
debugTestGeneration().catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});
