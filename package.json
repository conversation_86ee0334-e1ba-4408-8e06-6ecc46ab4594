{"name": "mcp-server-test-2", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "test:install": "playwright install --with-deps", "mcp:start": "mcp", "mcp:generate": "node scripts/generate-tests.js", "dev": "npm run mcp:start", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist playwright-report test-results", "setup": "npm install && npm run test:install", "pretest": "npm run build"}, "keywords": ["playwright", "testing", "mcp", "automation"], "author": "", "license": "ISC", "description": "Automated test suite using MCP server and Playwright", "devDependencies": {"@playwright/test": "^1.53.1", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "mcp": "^1.4.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "@modelcontextprotocol/server-filesystem": "^2025.3.28"}}