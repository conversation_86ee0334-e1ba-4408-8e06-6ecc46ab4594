import * as fs from 'fs/promises';
import * as path from 'path';
import { MCPPlaywrightClient, TestScenario, TestStep } from './mcp-client.js';
import { VerifyStatementParser, VerifyStatement } from './verify-parser.js';

export interface GeneratorConfig {
  outputDir: string;
  templateDir: string;
  defaultTimeout: number;
}

export class PlaywrightTestGenerator {
  private verifyParser = new VerifyStatementParser();

  constructor(
    private mcpClient: MCPPlaywrightClient,
    private config: GeneratorConfig
  ) {}

  async generateTestFromScenario(scenario: TestScenario): Promise<string> {
    try {
      // Load the test template
      const templatePath = path.join(this.config.templateDir, 'basic-test.template.ts');
      const template = await fs.readFile(templatePath, 'utf-8');

      // Generate test steps code
      const testStepsCode = this.generateTestStepsCode(scenario.steps || []);
      const assertionsCode = this.generateAssertionsCode(scenario.steps || []);

      // Replace template placeholders
      let testCode = template
        .replace('{{testSuiteName}}', scenario.name)
        .replace('{{testName}}', scenario.description)
        .replace('{{baseUrl}}', scenario.url)
        .replace('{{testSteps}}', testStepsCode)
        .replace('{{assertions}}', assertionsCode)
        .replace('{{beforeEachCode}}', '// Setup completed')
        .replace('{{afterEachCode}}', '// Cleanup completed');

      return testCode;
    } catch (error) {
      console.error('Failed to generate test from scenario:', error);
      throw error;
    }
  }

  async generateFromScenarioWithVerification(scenario: TestScenario): Promise<string> {
    if (scenario.verifyStatements && scenario.verifyStatements.length > 0) {
      // Use MCP to explore the website and generate comprehensive tests
      const explorationResults = await this.mcpClient.exploreWebsiteForVerification(
        scenario.url,
        scenario.verifyStatements
      );

      const testContent = this.buildTestFromVerificationExploration(scenario, explorationResults);
      const filename = `${scenario.id}.spec.ts`;
      const filepath = path.join(this.config.outputDir, filename);

      await fs.writeFile(filepath, testContent, 'utf-8');
      return filepath;
    } else {
      // Fall back to traditional step-based generation
      const testContent = await this.generateTestFromScenario(scenario);
      const filename = `${scenario.id}.spec.ts`;
      const filepath = path.join(this.config.outputDir, filename);

      await fs.writeFile(filepath, testContent, 'utf-8');
      return filepath;
    }
  }

  async generateFromWebsiteExploration(url: string, testName: string): Promise<string> {
    try {
      // Use MCP to actually explore the website and capture page info
      const explorationResult = await this.mcpClient.capturePageInfo(url);

      // Generate test based on exploration
      const testContent = this.buildTestFromExploration(url, testName, explorationResult);
      const filename = `${this.sanitizeFilename(testName)}.spec.ts`;
      const filepath = path.join(this.config.outputDir, filename);

      await fs.writeFile(filepath, testContent, 'utf-8');
      return filepath;
    } catch (error) {
      console.error('Failed to generate test from website exploration:', error);
      throw error;
    }
  }

  private generateTestStepsCode(steps: TestStep[]): string {
    return steps.map(step => {
      switch (step.action) {
        case 'navigate':
          return `    await page.goto('${step.target}');`;
        
        case 'click':
          return `    await page.locator('${step.target}').click();`;
        
        case 'fill':
          return `    await page.locator('${step.target}').fill('${step.value}');`;
        
        case 'wait':
          const timeout = step.timeout || this.config.defaultTimeout;
          return `    await page.waitForTimeout(${timeout});`;
        
        case 'screenshot':
          return `    await page.screenshot({ path: 'screenshots/${step.target}' });`;
        
        default:
          return `    // Unknown action: ${step.action}`;
      }
    }).join('\n');
  }

  private generateAssertionsCode(steps: TestStep[]): string {
    const verifySteps = steps.filter(step => step.action === 'verify');
    
    if (verifySteps.length === 0) {
      return '    // No additional assertions';
    }

    return verifySteps.map(step => {
      if (step.expected) {
        return `    await expect(page.locator('${step.target}')).toContainText('${step.expected}');`;
      } else {
        return `    await expect(page.locator('${step.target}')).toBeVisible();`;
      }
    }).join('\n');
  }

  async saveGeneratedTest(testCode: string, filename: string): Promise<string> {
    try {
      // Ensure output directory exists
      await fs.mkdir(this.config.outputDir, { recursive: true });

      // Generate full file path
      const filePath = path.join(this.config.outputDir, filename);

      // Write the test file
      await fs.writeFile(filePath, testCode, 'utf-8');

      console.log(`Generated test saved to: ${filePath}`);
      return filePath;
    } catch (error) {
      console.error('Failed to save generated test:', error);
      throw error;
    }
  }

  async generateTestsFromScenariosFile(scenariosPath: string): Promise<string[]> {
    try {
      // Read scenarios file
      const scenariosContent = await fs.readFile(scenariosPath, 'utf-8');
      const scenariosData = JSON.parse(scenariosContent);

      const generatedFiles: string[] = [];

      // Generate tests for each scenario
      for (const scenario of scenariosData.scenarios) {
        const testCode = await this.generateTestFromScenario(scenario);
        const filename = `${scenario.id}.spec.ts`;
        const filePath = await this.saveGeneratedTest(testCode, filename);
        generatedFiles.push(filePath);
      }

      return generatedFiles;
    } catch (error) {
      console.error('Failed to generate tests from scenarios file:', error);
      throw error;
    }
  }

  async exploreAndGenerateTest(url: string, testName: string): Promise<string> {
    try {
      // Use MCP client to explore the website
      const pageInfo = await this.mcpClient.capturePageInfo(url);

      // Parse the accessibility snapshot to extract meaningful elements
      const pageElements = this.parseAccessibilitySnapshot(pageInfo.snapshot);

      // Create a scenario based on actual page content
      const scenario: TestScenario = {
        id: testName.toLowerCase().replace(/\s+/g, '-'),
        name: testName,
        description: `Auto-generated test for ${url} based on page exploration`,
        url: url,
        steps: this.generateStepsFromPageElements(pageElements)
      };

      // Generate and save the test
      const testCode = await this.generateTestFromScenario(scenario);
      const filename = `${scenario.id}.spec.ts`;
      return await this.saveGeneratedTest(testCode, filename);
    } catch (error) {
      console.error('Failed to explore and generate test:', error);
      throw error;
    }
  }

  private buildTestFromVerificationExploration(scenario: TestScenario, explorationResults: any): string {
    const verificationTests = scenario.verifyStatements!.map((statement, index) => {
      const analysis = explorationResults.verificationAnalysis[index];
      const testActions = analysis.testActions || [];

      // Escape quotes in the statement for the test name
      const escapedStatement = statement.replace(/'/g, "\\'");

      return `
  test('${escapedStatement}', async ({ page }) => {
    // Navigate to the target URL
    await page.goto('${scenario.url}');

    // Wait for page to load
    await page.waitForLoadState('networkidle');

    ${testActions.join('\n    ')}
  });`;
    }).join('\n');

    return `import { test, expect } from '@playwright/test';

test.describe('${scenario.name}', () => {
  test.beforeEach(async ({ page }) => {
    // Setup code here
    await page.goto('${scenario.url}');
    await page.waitForLoadState('networkidle');
  });

${verificationTests}

  test.afterEach(async ({ page }) => {
    // Cleanup code here
    // Take screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: \`screenshots/\${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png\`,
        fullPage: true
      });
    }
  });
});
`;
  }

  private buildTestFromExploration(url: string, testName: string, explorationResult: any): string {
    // Parse the snapshot to get actual page elements
    const pageElements = this.parseAccessibilitySnapshot(explorationResult.snapshot);
    const specificTests = this.generateSpecificTestsFromElements(pageElements, url);

    return `import { test, expect } from '@playwright/test';

test.describe('${testName} - Exploration Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('${url}');
    await page.waitForLoadState('networkidle');
  });

  test('Page loads successfully with key elements', async ({ page }) => {
    // Verify page loads and basic elements are present
    await expect(page).toHaveTitle(/.+/);
    await expect(page.locator('body')).toBeVisible();

    // Take initial screenshot
    await page.screenshot({
      path: 'screenshots/${this.sanitizeFilename(testName)}-initial.png',
      fullPage: true
    });
  });

${specificTests}

  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: \`screenshots/\${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png\`,
        fullPage: true
      });
    }
  });
});
`;
  }

  private sanitizeFilename(name: string): string {
    return name.toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  private parseAccessibilitySnapshot(snapshot: any): any[] {
    const elements: any[] = [];

    try {
      // The snapshot is an array of content items from the MCP response
      if (Array.isArray(snapshot)) {
        for (const item of snapshot) {
          if (item.type === 'text' && item.text) {
            // Extract the YAML snapshot section
            const yamlMatch = item.text.match(/```yaml\n([\s\S]*?)\n```/);
            if (yamlMatch) {
              const yamlContent = yamlMatch[1];
              this.parseYamlSnapshot(yamlContent, elements);
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to parse accessibility snapshot:', error);
    }

    return elements;
  }

  private parseYamlSnapshot(yamlContent: string, elements: any[]): void {
    // Parse the YAML-like accessibility tree
    const lines = yamlContent.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed) continue;
      if (trimmed.startsWith('- generic') || trimmed.startsWith('- /url')) continue;

      // Parse different element types
      if (trimmed.includes('heading')) {
        const headingMatch = trimmed.match(/heading "([^"]+)"/);
        const refMatch = trimmed.match(/\[ref=([^\]]+)\]/);
        const levelMatch = trimmed.match(/\[level=(\d+)\]/);

        if (headingMatch) {
          elements.push({
            type: 'heading',
            text: headingMatch[1],
            ref: refMatch ? refMatch[1] : null,
            level: levelMatch ? parseInt(levelMatch[1]) : 1,
            selector: `h${levelMatch ? levelMatch[1] : '1'}`,
            role: 'heading'
          });
        }
      } else if (trimmed.includes('link')) {
        const linkMatch = trimmed.match(/link "([^"]+)"/);
        const refMatch = trimmed.match(/\[ref=([^\]]+)\]/);
        const urlMatch = trimmed.match(/\/url: ([^\s]+)/);

        if (linkMatch) {
          elements.push({
            type: 'link',
            text: linkMatch[1],
            ref: refMatch ? refMatch[1] : null,
            url: urlMatch ? urlMatch[1] : null,
            selector: `a:has-text("${linkMatch[1]}")`,
            role: 'link'
          });
        }
      } else if (trimmed.includes('button')) {
        const buttonMatch = trimmed.match(/button "([^"]+)"/);
        const refMatch = trimmed.match(/\[ref=([^\]]+)\]/);

        if (buttonMatch) {
          elements.push({
            type: 'button',
            text: buttonMatch[1],
            ref: refMatch ? refMatch[1] : null,
            selector: `button:has-text("${buttonMatch[1]}")`,
            role: 'button'
          });
        }
      } else if (trimmed.includes('textbox') || trimmed.includes('input')) {
        const inputMatch = trimmed.match(/(textbox|input) "([^"]+)"/);
        const refMatch = trimmed.match(/\[ref=([^\]]+)\]/);

        if (inputMatch) {
          elements.push({
            type: 'input',
            text: inputMatch[2],
            ref: refMatch ? refMatch[1] : null,
            selector: `input[placeholder*="${inputMatch[2]}"], input[name*="${inputMatch[2]}"]`,
            role: 'textbox'
          });
        }
      } else if (trimmed.includes('paragraph') && trimmed.includes(':')) {
        const paragraphMatch = trimmed.match(/paragraph.*?: (.+)/);
        const refMatch = trimmed.match(/\[ref=([^\]]+)\]/);

        if (paragraphMatch) {
          elements.push({
            type: 'paragraph',
            text: paragraphMatch[1],
            ref: refMatch ? refMatch[1] : null,
            selector: `p:has-text("${paragraphMatch[1].substring(0, 20)}")`,
            role: 'paragraph'
          });
        }
      }
    }
  }

  private extractElementsFromSnapshot(node: any, elements: any[], depth: number = 0): void {
    if (!node || depth > 10) return; // Prevent infinite recursion

    // Extract useful information from the node
    if (node.role || node.name || node.tag) {
      const element = {
        role: node.role,
        name: node.name,
        tag: node.tag,
        text: node.text || node.value,
        attributes: node.attributes || {},
        selector: this.generateSelectorFromNode(node),
        depth
      };

      // Only add elements that are likely to be testable
      if (this.isTestableElement(element)) {
        elements.push(element);
      }
    }

    // Recursively process children
    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        this.extractElementsFromSnapshot(child, elements, depth + 1);
      }
    }
  }

  private generateSelectorFromNode(node: any): string {
    // Generate a reasonable selector based on the node properties
    if (node.attributes?.id) {
      return `#${node.attributes.id}`;
    }

    if (node.attributes?.class) {
      const classes = node.attributes.class.split(' ').filter((c: string) => c.length > 0);
      if (classes.length > 0) {
        return `.${classes[0]}`;
      }
    }

    if (node.tag) {
      return node.tag;
    }

    if (node.role) {
      return `[role="${node.role}"]`;
    }

    if (node.name) {
      return `text="${node.name}"`;
    }

    return 'body';
  }

  private isTestableElement(element: any): boolean {
    // Determine if an element is worth testing
    const testableRoles = ['button', 'link', 'textbox', 'combobox', 'checkbox', 'radio', 'tab', 'menuitem'];
    const testableTags = ['button', 'a', 'input', 'select', 'textarea', 'form', 'nav', 'header', 'main', 'footer'];

    return (
      testableRoles.includes(element.role) ||
      testableTags.includes(element.tag) ||
      (element.name && element.name.length > 0 && element.name.length < 100) ||
      (element.text && element.text.length > 0 && element.text.length < 100)
    );
  }

  private generateStepsFromPageElements(elements: any[]): TestStep[] {
    const steps: TestStep[] = [
      { action: 'navigate', target: '/' },
      { action: 'screenshot', target: 'initial-page.png' }
    ];

    // Generate verification steps for important elements
    for (const element of elements.slice(0, 10)) { // Limit to first 10 elements
      if (element.role === 'button' || element.tag === 'button') {
        steps.push({
          action: 'verify',
          target: element.selector,
          expected: element.name || element.text || 'button'
        });
      } else if (element.role === 'link' || element.tag === 'a') {
        steps.push({
          action: 'verify',
          target: element.selector,
          expected: element.name || element.text || 'link'
        });
      } else if (element.tag === 'input' || element.role === 'textbox') {
        steps.push({
          action: 'verify',
          target: element.selector,
          expected: ''
        });
      } else if (element.name || element.text) {
        steps.push({
          action: 'verify',
          target: element.selector,
          expected: element.name || element.text
        });
      }
    }

    return steps;
  }

  private generateSpecificTestsFromElements(elements: any[], url: string): string {
    const tests: string[] = [];

    // Group elements by type
    const buttons = elements.filter(e => e.type === 'button' || e.role === 'button');
    const links = elements.filter(e => e.type === 'link' || e.role === 'link');
    const inputs = elements.filter(e => e.type === 'input' || e.role === 'textbox');
    const headings = elements.filter(e => e.type === 'heading' || e.role === 'heading');
    const paragraphs = elements.filter(e => e.type === 'paragraph');

    // Generate heading tests
    if (headings.length > 0) {
      const headingTests = headings.map(heading => {
        const headingText = heading.text.replace(/['"]/g, '\\"');
        return `
    // Test heading: ${heading.text.substring(0, 50)}
    await expect(page.locator('${heading.selector}')).toBeVisible();
    await expect(page.locator('${heading.selector}')).toContainText('${headingText}');`;
      }).join('');

      tests.push(`
  test('Page headings are present and contain expected text', async ({ page }) => {${headingTests}
  });`);
    }

    // Generate link tests
    if (links.length > 0) {
      const linkTests = links.map(link => {
        const linkText = link.text.replace(/['"]/g, '\\"');
        return `
    // Test link: ${link.text.substring(0, 50)}
    await expect(page.locator('${link.selector}')).toBeVisible();
    await expect(page.locator('${link.selector}')).toContainText('${linkText}');`;
      }).join('');

      tests.push(`
  test('Links are present and accessible', async ({ page }) => {${linkTests}
  });`);
    }

    // Generate button interaction tests
    if (buttons.length > 0) {
      const buttonTests = buttons.map(button => {
        const buttonText = button.text.replace(/['"]/g, '\\"');
        return `
    // Test button: ${button.text.substring(0, 50)}
    await expect(page.locator('${button.selector}')).toBeVisible();
    await expect(page.locator('${button.selector}')).toContainText('${buttonText}');`;
      }).join('');

      tests.push(`
  test('Interactive buttons are present and clickable', async ({ page }) => {${buttonTests}
  });`);
    }

    // Generate form input tests
    if (inputs.length > 0) {
      const inputTests = inputs.map(input => {
        return `
    // Test input: ${input.text.substring(0, 50)}
    await expect(page.locator('${input.selector}')).toBeVisible();`;
      }).join('');

      tests.push(`
  test('Form inputs are present and functional', async ({ page }) => {${inputTests}
  });`);
    }

    // Generate content verification tests
    if (paragraphs.length > 0) {
      const contentTests = paragraphs.slice(0, 3).map(paragraph => {
        const contentText = paragraph.text.substring(0, 100).replace(/['"]/g, '\\"');
        return `
    // Test content: ${paragraph.text.substring(0, 50)}...
    await expect(page.locator('${paragraph.selector}')).toBeVisible();`;
      }).join('');

      tests.push(`
  test('Page content is present and readable', async ({ page }) => {${contentTests}
  });`);
    }

    return tests.join('\n');
  }
}
