#!/usr/bin/env node

const { MCPPlaywrightClient } = require('./src/mcp-client.js');

async function debugMCP() {
  console.log('🔍 Debugging MCP connection...');
  
  const mcpClient = new MCPPlaywrightClient({
    command: 'npx',
    args: ['@playwright/mcp@latest', '--headless'],
    env: {}
  });

  try {
    // Connect to MCP server
    await mcpClient.connect();
    console.log('✅ Connected to MCP server');

    // List available tools first
    console.log('🔧 Listing available tools...');
    const tools = await mcpClient.client.listTools();
    console.log('Available tools:');
    tools.tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });

    // Test website exploration
    console.log('🌐 Exploring https://example.com...');
    const result = await mcpClient.exploreWebsite('https://example.com');

    console.log('📊 Raw exploration result:');
    console.log(JSON.stringify(result, null, 2));

    console.log('📊 Exploration result properties:');
    console.log('URL:', result?.url);
    console.log('Timestamp:', result?.timestamp);
    console.log('Snapshot type:', typeof result?.snapshot);
    console.log('Snapshot length:', result?.snapshot ? result.snapshot.length : 'null');

    if (result?.snapshot) {
      console.log('Snapshot preview:');
      if (Array.isArray(result.snapshot)) {
        result.snapshot.forEach((item, index) => {
          console.log(`Snapshot item ${index}:`, item.text?.substring(0, 500) || item);
        });
      } else {
        console.log(result.snapshot.substring(0, 500));
      }
    }

    if (result?.consoleMessages) {
      console.log('Console messages:', result.consoleMessages);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    // Cleanup
    await mcpClient.disconnect();
    console.log('🔌 Disconnected from MCP server');
  }
}

// Run the debug function
debugMCP().catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});
