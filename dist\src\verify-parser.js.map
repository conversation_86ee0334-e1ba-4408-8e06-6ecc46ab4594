{"version": 3, "file": "verify-parser.js", "sourceRoot": "", "sources": ["../../src/verify-parser.ts"], "names": [], "mappings": ";;;AASA,MAAa,qBAAqB;IAEhC,oBAAoB,CAAC,SAAiB;QACpC,MAAM,MAAM,GAAoB;YAC9B,SAAS,EAAE,SAAS,CAAC,WAAW,EAAE;YAClC,SAAS,EAAE,EAAE;SACd,CAAC;QAEF,6CAA6C;QAC7C,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC;YAChC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC;YAC5B,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,GAAG,iBAAiB,CAAC;YAClC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC;YAC7B,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,4BAA4B;YAC5B,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC;YAChC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAChD,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,SAAiB;QACvC,MAAM,gBAAgB,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;QAC3F,OAAO,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACrF,CAAC;IAEO,WAAW,CAAC,SAAiB;QACnC,MAAM,YAAY,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC/E,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACjF,CAAC;IAEO,iBAAiB,CAAC,SAAiB;QACzC,MAAM,kBAAkB,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QACxF,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IACvF,CAAC;IAEO,YAAY,CAAC,SAAiB;QACpC,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACzF,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAClF,CAAC;IAEO,cAAc,CAAC,SAAiB;QACtC,8CAA8C;QAC9C,MAAM,eAAe,GAA2B;YAC9C,QAAQ,EAAE,QAAQ;YAClB,aAAa,EAAE,oBAAoB;YACnC,YAAY,EAAE,KAAK;YACnB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,qBAAqB;YAC7B,iBAAiB,EAAE,wBAAwB;YAC3C,MAAM,EAAE,MAAM;YACd,cAAc,EAAE,oDAAoD;YACpE,YAAY,EAAE,8CAA8C;YAC5D,aAAa,EAAE,2CAA2C;YAC1D,gBAAgB,EAAE,iDAAiD;YACnE,eAAe,EAAE,6CAA6C;YAC9D,oBAAoB,EAAE,2DAA2D;YACjF,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,wBAAwB;YACnC,cAAc,EAAE,IAAI;YACpB,OAAO,EAAE,WAAW;YACpB,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,iBAAiB;YAC5B,SAAS,EAAE,0BAA0B;YACrC,YAAY,EAAE,6CAA6C;YAC3D,eAAe,EAAE,qEAAqE;SACvF,CAAC;QAEF,+BAA+B;QAC/B,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;QAEpF,KAAK,MAAM,OAAO,IAAI,UAAU,EAAE,CAAC;YACjC,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC9C,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,mBAAmB;IACpC,CAAC;IAEO,mBAAmB,CAAC,SAAiB;QAC3C,8BAA8B;QAC9B,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC1D,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QAED,gCAAgC;QAChC,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACvE,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,2CAA2C;QAC3C,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC9E,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,gBAAgB,CAAC,SAAiB;QACxC,IAAI,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACtD,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACpD,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QAChD,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACpD,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QAChD,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QACpD,IAAI,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACtD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAClD,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAElD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,iBAAiB,CAAC,OAAe;QACvC,oDAAoD;QACpD,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,CAAC;QAE5B,wBAAwB;QACxB,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,sBAAsB,EAAE,sBAAsB,EAAE,MAAM,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,oBAAoB;IACtD,CAAC;IAED,mBAAmB,CAAC,MAAuB;;QACzC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,eAAe,GAAG,CAAA,MAAA,MAAM,CAAC,SAAS,0CAAG,CAAC,CAAC,KAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC;QAE1E,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,eAAe;gBAClB,OAAO,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,OAAO,aAAa,CAAC,CAAC;gBACvD,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,oBAAoB,CAAC,CAAC;gBAChF,MAAM;YAER,KAAK,WAAW;gBACd,OAAO,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,OAAO,mBAAmB,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;gBAClF,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,sBAAsB,MAAM,CAAC,YAAY,KAAK,CAAC,CAAC;gBAC1G,MAAM;YAER,KAAK,iBAAiB;gBACpB,OAAO,CAAC,IAAI,CAAC,qCAAqC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;gBACpE,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,OAAO,EAAE,CAAC;oBACnE,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;oBAC5D,OAAO,CAAC,IAAI,CAAC,uBAAuB,eAAe,2BAA2B,CAAC,CAAC;oBAChF,OAAO,CAAC,IAAI,CAAC,uBAAuB,eAAe,YAAY,CAAC,CAAC;oBACjE,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,KAAK,eAAe,qCAAqC,CAAC,CAAC;gBACvH,CAAC;gBACD,MAAM;YAER,KAAK,YAAY;gBACf,OAAO,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;gBACvE,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;oBACpC,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,qBAAqB,CAAC,CAAC;gBACnF,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC1C,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,oBAAoB,CAAC,CAAC;gBAClF,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC1C,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,oBAAoB,CAAC,CAAC;gBAClF,CAAC;qBAAM,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;oBACzC,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,mBAAmB,CAAC,CAAC;gBACjF,CAAC;gBACD,MAAM;YAER;gBACE,OAAO,CAAC,IAAI,CAAC,gCAAgC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;gBACjE,OAAO,CAAC,IAAI,CAAC,8BAA8B,eAAe,oBAAoB,CAAC,CAAC;QACpF,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA5MD,sDA4MC"}