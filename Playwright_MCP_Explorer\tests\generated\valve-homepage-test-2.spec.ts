import { test, expect } from '@playwright/test';

test.describe('Valve-Homepage-Test 2 - Exploration Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('https://www.valvesoftware.com/en/');
    await page.waitForLoadState('networkidle');
  });

  test('Page loads successfully with key elements', async ({ page }) => {
    // Verify page loads and basic elements are present
    await expect(page).toHaveTitle(/.+/);
    await expect(page.locator('body')).toBeVisible();

    // Take initial screenshot
    await page.screenshot({
      path: 'screenshots/valve-homepage-test-2-initial.png',
      fullPage: true
    });
  });


  test('Page headings are present and contain expected text', async ({ page }) => {
    // Test heading: We make games, Steam, and hardware. Join us.
    await expect(page.locator('h3')).toBeVisible();
    await expect(page.locator('h3')).toContainText('We make games, Steam, and hardware. Join us.');
    // Test heading: 3D Character Artist
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('3D Character Artist');
    // Test heading: Animator
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Animator');
    // Test heading: Effects Artist
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Effects Artist');
    // Test heading: 3D Environment Artist
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('3D Environment Artist');
    // Test heading: Senior 3D Environment Artist (Full-time) – Multipl
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Senior 3D Environment Artist (Full-time) – Multiple Positions');
    // Test heading: Sound Designer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Sound Designer');
    // Test heading: Audio Software Engineer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Audio Software Engineer');
    // Test heading: Business Development Steam Team
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Business Development Steam Team');
    // Test heading: Steam Partner Technical Account Manager
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Steam Partner Technical Account Manager');
    // Test heading: Business Development Other
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Business Development Other');
    // Test heading: Steam Support Leadership
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Steam Support Leadership');
    // Test heading: Economist
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Economist');
    // Test heading: Psychologist Research/Experimental
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Psychologist Research/Experimental');
    // Test heading: Statistician / Data Scientist
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Statistician / Data Scientist');
    // Test heading: Did we miss something?
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Did we miss something?');
    // Test heading: Outbound Royalty Payments Professional
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Outbound Royalty Payments Professional');
    // Test heading: Finance Other
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Finance Other');
    // Test heading: Level Designer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Level Designer');
    // Test heading: Psychologist Research/Experimental
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Psychologist Research/Experimental');
    // Test heading: Sound Designer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Sound Designer');
    // Test heading: Game Development Software Engineer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Game Development Software Engineer');
    // Test heading: Game Design Other
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Game Design Other');
    // Test heading: Electrical Engineer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Electrical Engineer');
    // Test heading: Industrial Designer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Industrial Designer');
    // Test heading: Software Engineer for HW
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Software Engineer for HW');
    // Test heading: Sustaining Engineer for HW
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Sustaining Engineer for HW');
    // Test heading: Legal Team
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Legal Team');
    // Test heading: Industrial Designer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Industrial Designer');
    // Test heading: Design Visual & User Experience
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Design Visual & User Experience');
    // Test heading: Design Other
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Design Other');
    // Test heading: Steam Software Engineer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Steam Software Engineer');
    // Test heading: Steam Database Administrator
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Steam Database Administrator');
    // Test heading: Steam Partner Technical Account Manager
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Steam Partner Technical Account Manager');
    // Test heading: Software Engineer for HW
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Software Engineer for HW');
    // Test heading: Game Development Software Engineer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Game Development Software Engineer');
    // Test heading: Computer Vision Software Engineer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Computer Vision Software Engineer');
    // Test heading: Audio Software Engineer
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Audio Software Engineer');
    // Test heading: Software Engineering Other
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Software Engineering Other');
    // Test heading: Steam Database Administrator
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Steam Database Administrator');
    // Test heading: Valve Corporation
    await expect(page.locator('h5')).toBeVisible();
    await expect(page.locator('h5')).toContainText('Valve Corporation');
  });

  test('Links are present and accessible', async ({ page }) => {
    // Test link: We make games, Steam, and hardware. Join us.
    await expect(page.locator('a:has-text("We make games, Steam, and hardware. Join us.")')).toBeVisible();
    await expect(page.locator('a:has-text("We make games, Steam, and hardware. Join us.")')).toContainText('We make games, Steam, and hardware. Join us.');
    // Test link: Art
    await expect(page.locator('a:has-text("Art")')).toBeVisible();
    await expect(page.locator('a:has-text("Art")')).toContainText('Art');
    // Test link: 3D Character Artist
    await expect(page.locator('a:has-text("3D Character Artist")')).toBeVisible();
    await expect(page.locator('a:has-text("3D Character Artist")')).toContainText('3D Character Artist');
    // Test link: Animator
    await expect(page.locator('a:has-text("Animator")')).toBeVisible();
    await expect(page.locator('a:has-text("Animator")')).toContainText('Animator');
    // Test link: Effects Artist
    await expect(page.locator('a:has-text("Effects Artist")')).toBeVisible();
    await expect(page.locator('a:has-text("Effects Artist")')).toContainText('Effects Artist');
    // Test link: 3D Environment Artist
    await expect(page.locator('a:has-text("3D Environment Artist")')).toBeVisible();
    await expect(page.locator('a:has-text("3D Environment Artist")')).toContainText('3D Environment Artist');
    // Test link: Senior 3D Environment Artist (Full-time) – Multipl
    await expect(page.locator('a:has-text("Senior 3D Environment Artist (Full-time) – Multiple Positions")')).toBeVisible();
    await expect(page.locator('a:has-text("Senior 3D Environment Artist (Full-time) – Multiple Positions")')).toContainText('Senior 3D Environment Artist (Full-time) – Multiple Positions');
    // Test link: Audio
    await expect(page.locator('a:has-text("Audio")')).toBeVisible();
    await expect(page.locator('a:has-text("Audio")')).toContainText('Audio');
    // Test link: Sound Designer
    await expect(page.locator('a:has-text("Sound Designer")')).toBeVisible();
    await expect(page.locator('a:has-text("Sound Designer")')).toContainText('Sound Designer');
    // Test link: Audio Software Engineer
    await expect(page.locator('a:has-text("Audio Software Engineer")')).toBeVisible();
    await expect(page.locator('a:has-text("Audio Software Engineer")')).toContainText('Audio Software Engineer');
    // Test link: Business Development
    await expect(page.locator('a:has-text("Business Development")')).toBeVisible();
    await expect(page.locator('a:has-text("Business Development")')).toContainText('Business Development');
    // Test link: Business Development Steam Team
    await expect(page.locator('a:has-text("Business Development Steam Team")')).toBeVisible();
    await expect(page.locator('a:has-text("Business Development Steam Team")')).toContainText('Business Development Steam Team');
    // Test link: Steam Partner Technical Account Manager
    await expect(page.locator('a:has-text("Steam Partner Technical Account Manager")')).toBeVisible();
    await expect(page.locator('a:has-text("Steam Partner Technical Account Manager")')).toContainText('Steam Partner Technical Account Manager');
    // Test link: Business Development Other
    await expect(page.locator('a:has-text("Business Development Other")')).toBeVisible();
    await expect(page.locator('a:has-text("Business Development Other")')).toContainText('Business Development Other');
    // Test link: Customer Support
    await expect(page.locator('a:has-text("Customer Support")')).toBeVisible();
    await expect(page.locator('a:has-text("Customer Support")')).toContainText('Customer Support');
    // Test link: Steam Support Leadership
    await expect(page.locator('a:has-text("Steam Support Leadership")')).toBeVisible();
    await expect(page.locator('a:has-text("Steam Support Leadership")')).toContainText('Steam Support Leadership');
    // Test link: Data Science
    await expect(page.locator('a:has-text("Data Science")')).toBeVisible();
    await expect(page.locator('a:has-text("Data Science")')).toContainText('Data Science');
    // Test link: Economist
    await expect(page.locator('a:has-text("Economist")')).toBeVisible();
    await expect(page.locator('a:has-text("Economist")')).toContainText('Economist');
    // Test link: Psychologist Research/Experimental
    await expect(page.locator('a:has-text("Psychologist Research/Experimental")')).toBeVisible();
    await expect(page.locator('a:has-text("Psychologist Research/Experimental")')).toContainText('Psychologist Research/Experimental');
    // Test link: Statistician / Data Scientist
    await expect(page.locator('a:has-text("Statistician / Data Scientist")')).toBeVisible();
    await expect(page.locator('a:has-text("Statistician / Data Scientist")')).toContainText('Statistician / Data Scientist');
    // Test link: Did we miss something?
    await expect(page.locator('a:has-text("Did we miss something?")')).toBeVisible();
    await expect(page.locator('a:has-text("Did we miss something?")')).toContainText('Did we miss something?');
    // Test link: Finance
    await expect(page.locator('a:has-text("Finance")')).toBeVisible();
    await expect(page.locator('a:has-text("Finance")')).toContainText('Finance');
    // Test link: Outbound Royalty Payments Professional
    await expect(page.locator('a:has-text("Outbound Royalty Payments Professional")')).toBeVisible();
    await expect(page.locator('a:has-text("Outbound Royalty Payments Professional")')).toContainText('Outbound Royalty Payments Professional');
    // Test link: Finance Other
    await expect(page.locator('a:has-text("Finance Other")')).toBeVisible();
    await expect(page.locator('a:has-text("Finance Other")')).toContainText('Finance Other');
    // Test link: Game Design
    await expect(page.locator('a:has-text("Game Design")')).toBeVisible();
    await expect(page.locator('a:has-text("Game Design")')).toContainText('Game Design');
    // Test link: Level Designer
    await expect(page.locator('a:has-text("Level Designer")')).toBeVisible();
    await expect(page.locator('a:has-text("Level Designer")')).toContainText('Level Designer');
    // Test link: Psychologist Research/Experimental
    await expect(page.locator('a:has-text("Psychologist Research/Experimental")')).toBeVisible();
    await expect(page.locator('a:has-text("Psychologist Research/Experimental")')).toContainText('Psychologist Research/Experimental');
    // Test link: Sound Designer
    await expect(page.locator('a:has-text("Sound Designer")')).toBeVisible();
    await expect(page.locator('a:has-text("Sound Designer")')).toContainText('Sound Designer');
    // Test link: Game Development Software Engineer
    await expect(page.locator('a:has-text("Game Development Software Engineer")')).toBeVisible();
    await expect(page.locator('a:has-text("Game Development Software Engineer")')).toContainText('Game Development Software Engineer');
    // Test link: Game Design Other
    await expect(page.locator('a:has-text("Game Design Other")')).toBeVisible();
    await expect(page.locator('a:has-text("Game Design Other")')).toContainText('Game Design Other');
    // Test link: Hardware
    await expect(page.locator('a:has-text("Hardware")')).toBeVisible();
    await expect(page.locator('a:has-text("Hardware")')).toContainText('Hardware');
    // Test link: Electrical Engineer
    await expect(page.locator('a:has-text("Electrical Engineer")')).toBeVisible();
    await expect(page.locator('a:has-text("Electrical Engineer")')).toContainText('Electrical Engineer');
    // Test link: Industrial Designer
    await expect(page.locator('a:has-text("Industrial Designer")')).toBeVisible();
    await expect(page.locator('a:has-text("Industrial Designer")')).toContainText('Industrial Designer');
    // Test link: Software Engineer for HW
    await expect(page.locator('a:has-text("Software Engineer for HW")')).toBeVisible();
    await expect(page.locator('a:has-text("Software Engineer for HW")')).toContainText('Software Engineer for HW');
    // Test link: Sustaining Engineer for HW
    await expect(page.locator('a:has-text("Sustaining Engineer for HW")')).toBeVisible();
    await expect(page.locator('a:has-text("Sustaining Engineer for HW")')).toContainText('Sustaining Engineer for HW');
    // Test link: Legal
    await expect(page.locator('a:has-text("Legal")')).toBeVisible();
    await expect(page.locator('a:has-text("Legal")')).toContainText('Legal');
    // Test link: Legal Team
    await expect(page.locator('a:has-text("Legal Team")')).toBeVisible();
    await expect(page.locator('a:has-text("Legal Team")')).toContainText('Legal Team');
    // Test link: Product Design
    await expect(page.locator('a:has-text("Product Design")')).toBeVisible();
    await expect(page.locator('a:has-text("Product Design")')).toContainText('Product Design');
    // Test link: Industrial Designer
    await expect(page.locator('a:has-text("Industrial Designer")')).toBeVisible();
    await expect(page.locator('a:has-text("Industrial Designer")')).toContainText('Industrial Designer');
    // Test link: Design Visual & User Experience
    await expect(page.locator('a:has-text("Design Visual & User Experience")')).toBeVisible();
    await expect(page.locator('a:has-text("Design Visual & User Experience")')).toContainText('Design Visual & User Experience');
    // Test link: Design Other
    await expect(page.locator('a:has-text("Design Other")')).toBeVisible();
    await expect(page.locator('a:has-text("Design Other")')).toContainText('Design Other');
    // Test link: Software Engineering
    await expect(page.locator('a:has-text("Software Engineering")')).toBeVisible();
    await expect(page.locator('a:has-text("Software Engineering")')).toContainText('Software Engineering');
    // Test link: Steam Software Engineer
    await expect(page.locator('a:has-text("Steam Software Engineer")')).toBeVisible();
    await expect(page.locator('a:has-text("Steam Software Engineer")')).toContainText('Steam Software Engineer');
    // Test link: Steam Database Administrator
    await expect(page.locator('a:has-text("Steam Database Administrator")')).toBeVisible();
    await expect(page.locator('a:has-text("Steam Database Administrator")')).toContainText('Steam Database Administrator');
    // Test link: Steam Partner Technical Account Manager
    await expect(page.locator('a:has-text("Steam Partner Technical Account Manager")')).toBeVisible();
    await expect(page.locator('a:has-text("Steam Partner Technical Account Manager")')).toContainText('Steam Partner Technical Account Manager');
    // Test link: Software Engineer for HW
    await expect(page.locator('a:has-text("Software Engineer for HW")')).toBeVisible();
    await expect(page.locator('a:has-text("Software Engineer for HW")')).toContainText('Software Engineer for HW');
    // Test link: Game Development Software Engineer
    await expect(page.locator('a:has-text("Game Development Software Engineer")')).toBeVisible();
    await expect(page.locator('a:has-text("Game Development Software Engineer")')).toContainText('Game Development Software Engineer');
    // Test link: Computer Vision Software Engineer
    await expect(page.locator('a:has-text("Computer Vision Software Engineer")')).toBeVisible();
    await expect(page.locator('a:has-text("Computer Vision Software Engineer")')).toContainText('Computer Vision Software Engineer');
    // Test link: Audio Software Engineer
    await expect(page.locator('a:has-text("Audio Software Engineer")')).toBeVisible();
    await expect(page.locator('a:has-text("Audio Software Engineer")')).toContainText('Audio Software Engineer');
    // Test link: Software Engineering Other
    await expect(page.locator('a:has-text("Software Engineering Other")')).toBeVisible();
    await expect(page.locator('a:has-text("Software Engineering Other")')).toContainText('Software Engineering Other');
    // Test link: Technical Infrastructure
    await expect(page.locator('a:has-text("Technical Infrastructure")')).toBeVisible();
    await expect(page.locator('a:has-text("Technical Infrastructure")')).toContainText('Technical Infrastructure');
    // Test link: Steam Database Administrator
    await expect(page.locator('a:has-text("Steam Database Administrator")')).toBeVisible();
    await expect(page.locator('a:has-text("Steam Database Administrator")')).toContainText('Steam Database Administrator');
    // Test link: Did we miss something?
    await expect(page.locator('a:has-text("Did we miss something?")')).toBeVisible();
    await expect(page.locator('a:has-text("Did we miss something?")')).toContainText('Did we miss something?');
    // Test link: Valve Corporation
    await expect(page.locator('a:has-text("Valve Corporation")')).toBeVisible();
    await expect(page.locator('a:has-text("Valve Corporation")')).toContainText('Valve Corporation');
    // Test link: People
    await expect(page.locator('a:has-text("People")')).toBeVisible();
    await expect(page.locator('a:has-text("People")')).toContainText('People');
    // Test link: Press Inquiries
    await expect(page.locator('a:has-text("Press Inquiries")')).toBeVisible();
    await expect(page.locator('a:has-text("Press Inquiries")')).toContainText('Press Inquiries');
    // Test link: About Us
    await expect(page.locator('a:has-text("About Us")')).toBeVisible();
    await expect(page.locator('a:has-text("About Us")')).toContainText('About Us');
    // Test link: Jobs
    await expect(page.locator('a:has-text("Jobs")')).toBeVisible();
    await expect(page.locator('a:has-text("Jobs")')).toContainText('Jobs');
    // Test link: Steam Partners
    await expect(page.locator('a:has-text("Steam Partners")')).toBeVisible();
    await expect(page.locator('a:has-text("Steam Partners")')).toContainText('Steam Partners');
    // Test link: Publications
    await expect(page.locator('a:has-text("Publications")')).toBeVisible();
    await expect(page.locator('a:has-text("Publications")')).toContainText('Publications');
    // Test link: Site Terms of Use
    await expect(page.locator('a:has-text("Site Terms of Use")')).toBeVisible();
    await expect(page.locator('a:has-text("Site Terms of Use")')).toContainText('Site Terms of Use');
    // Test link: Privacy Policy
    await expect(page.locator('a:has-text("Privacy Policy")')).toBeVisible();
    await expect(page.locator('a:has-text("Privacy Policy")')).toContainText('Privacy Policy');
    // Test link: Security
    await expect(page.locator('a:has-text("Security")')).toBeVisible();
    await expect(page.locator('a:has-text("Security")')).toContainText('Security');
    // Test link: Legal
    await expect(page.locator('a:has-text("Legal")')).toBeVisible();
    await expect(page.locator('a:has-text("Legal")')).toContainText('Legal');
  });

  test('Interactive buttons are present and clickable', async ({ page }) => {
    // Test button: Search
    await expect(page.locator('button:has-text("Search")')).toBeVisible();
    await expect(page.locator('button:has-text("Search")')).toContainText('Search');
  });

  test('Form inputs are present and functional', async ({ page }) => {
    // Test input: Search Jobs
    await expect(page.locator('input[placeholder*="Search Jobs"], input[name*="Search Jobs"]')).toBeVisible();
  });

  test('Page content is present and readable', async ({ page }) => {
    // Test content: "We aren't a typical company—which is why you're h...
    await expect(page.locator('p:has-text(""We aren't a typical")')).toBeVisible();
    // Test content: We hire people with broad skill sets who also exhi...
    await expect(page.locator('p:has-text("We hire people with ")')).toBeVisible();
    // Test content: We hire people with broad skill sets who also exhi...
    await expect(page.locator('p:has-text("We hire people with ")')).toBeVisible();
  });

  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: `screenshots/${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png`,
        fullPage: true
      });
    }
  });
});
