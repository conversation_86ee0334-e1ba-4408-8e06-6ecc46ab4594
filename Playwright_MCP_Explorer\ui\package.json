{"name": "testsynth-ui", "version": "1.0.0", "private": true, "dependencies": {"@types/node": "^18.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "web-vitals": "^3.0.0", "axios": "^1.6.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}