# PowerShell script to start TestSynth MCP Explorer UI

Write-Host "🚀 Starting TestSynth MCP Explorer UI..." -ForegroundColor Green
Write-Host ""

# Start the backend server in a new PowerShell window
Write-Host "📡 Starting backend API server..." -ForegroundColor Yellow
$serverPath = Join-Path $PSScriptRoot "ui\server"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$serverPath'; npm start" -WindowStyle Normal

# Wait a moment for server to start
Start-Sleep -Seconds 3

# Start the React frontend in a new PowerShell window
Write-Host "🎨 Starting React frontend..." -ForegroundColor Yellow
$frontendPath = Join-Path $PSScriptRoot "ui"
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$frontendPath'; npm start" -WindowStyle Normal

Write-Host ""
Write-Host "✅ UI is starting up!" -ForegroundColor Green
Write-Host "📡 Backend API: http://localhost:3001" -ForegroundColor Cyan
Write-Host "🎨 Frontend UI: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "The browser should open automatically in a few seconds..." -ForegroundColor Gray
Write-Host "Press any key to exit this script (the UI will continue running)..." -ForegroundColor Gray

$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
