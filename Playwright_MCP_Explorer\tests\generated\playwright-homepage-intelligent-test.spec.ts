import { test, expect } from '@playwright/test';

test.describe('Playwright Homepage Intelligent Test - Exploration Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('https://playwright.dev');
    await page.waitForLoadState('networkidle');
  });

  test('Page loads successfully with key elements', async ({ page }) => {
    // Verify page loads and basic elements are present
    await expect(page).toHaveTitle(/.+/);
    await expect(page.locator('body')).toBeVisible();

    // Take initial screenshot
    await page.screenshot({
      path: 'screenshots/playwright-homepage-intelligent-test-initial.png',
      fullPage: true
    });
  });


  test('Page headings are present and contain expected text', async ({ page }) => {
    // Test heading: Playwright enables reliable end-to-end testing for
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h1')).toContainText('Playwright enables reliable end-to-end testing for modern web apps.');
    // Test heading: Any browser • Any platform • One API
    await expect(page.locator('h3')).toBeVisible();
    await expect(page.locator('h3')).toContainText('Any browser • Any platform • One API');
    // Test heading: Resilient • No flaky tests
    await expect(page.locator('h3')).toBeVisible();
    await expect(page.locator('h3')).toContainText('Resilient • No flaky tests');
    // Test heading: No trade-offs • No limits
    await expect(page.locator('h3')).toBeVisible();
    await expect(page.locator('h3')).toContainText('No trade-offs • No limits');
    // Test heading: Full isolation • Fast execution
    await expect(page.locator('h3')).toBeVisible();
    await expect(page.locator('h3')).toContainText('Full isolation • Fast execution');
    // Test heading: Powerful Tooling
    await expect(page.locator('h3')).toBeVisible();
    await expect(page.locator('h3')).toContainText('Powerful Tooling');
    // Test heading: Chosen by companies and open source projects
    await expect(page.locator('h2')).toBeVisible();
    await expect(page.locator('h2')).toContainText('Chosen by companies and open source projects');
  });

  test('Links are present and accessible', async ({ page }) => {
    // Test link: Skip to main content
    await expect(page.locator('a:has-text("Skip to main content")')).toBeVisible();
    await expect(page.locator('a:has-text("Skip to main content")')).toContainText('Skip to main content');
    // Test link: Playwright logo Playwright
    await expect(page.locator('a:has-text("Playwright logo Playwright")')).toBeVisible();
    await expect(page.locator('a:has-text("Playwright logo Playwright")')).toContainText('Playwright logo Playwright');
    // Test link: Get started
    await expect(page.locator('a:has-text("Get started")')).toBeVisible();
    await expect(page.locator('a:has-text("Get started")')).toContainText('Get started');
    // Test link: Star microsoft/playwright on GitHub
    await expect(page.locator('a:has-text("Star microsoft/playwright on GitHub")')).toBeVisible();
    await expect(page.locator('a:has-text("Star microsoft/playwright on GitHub")')).toContainText('Star microsoft/playwright on GitHub');
    // Test link: 74k+ stargazers on GitHub
    await expect(page.locator('a:has-text("74k+ stargazers on GitHub")')).toBeVisible();
    await expect(page.locator('a:has-text("74k+ stargazers on GitHub")')).toContainText('74k+ stargazers on GitHub');
    // Test link: TypeScript
    await expect(page.locator('a:has-text("TypeScript")')).toBeVisible();
    await expect(page.locator('a:has-text("TypeScript")')).toContainText('TypeScript');
    // Test link: JavaScript
    await expect(page.locator('a:has-text("JavaScript")')).toBeVisible();
    await expect(page.locator('a:has-text("JavaScript")')).toContainText('JavaScript');
    // Test link: Python
    await expect(page.locator('a:has-text("Python")')).toBeVisible();
    await expect(page.locator('a:has-text("Python")')).toContainText('Python');
    // Test link: .NET
    await expect(page.locator('a:has-text(".NET")')).toBeVisible();
    await expect(page.locator('a:has-text(".NET")')).toContainText('.NET');
    // Test link: Java
    await expect(page.locator('a:has-text("Java")')).toBeVisible();
    await expect(page.locator('a:has-text("Java")')).toContainText('Java');
    // Test link: Codegen.
    await expect(page.locator('a:has-text("Codegen.")')).toBeVisible();
    await expect(page.locator('a:has-text("Codegen.")')).toContainText('Codegen.');
    // Test link: Playwright inspector.
    await expect(page.locator('a:has-text("Playwright inspector.")')).toBeVisible();
    await expect(page.locator('a:has-text("Playwright inspector.")')).toContainText('Playwright inspector.');
    // Test link: Trace Viewer.
    await expect(page.locator('a:has-text("Trace Viewer.")')).toBeVisible();
    await expect(page.locator('a:has-text("Trace Viewer.")')).toContainText('Trace Viewer.');
    // Test link: VS Code
    await expect(page.locator('a:has-text("VS Code")')).toBeVisible();
    await expect(page.locator('a:has-text("VS Code")')).toContainText('VS Code');
    // Test link: Bing
    await expect(page.locator('a:has-text("Bing")')).toBeVisible();
    await expect(page.locator('a:has-text("Bing")')).toContainText('Bing');
    // Test link: Outlook
    await expect(page.locator('a:has-text("Outlook")')).toBeVisible();
    await expect(page.locator('a:has-text("Outlook")')).toContainText('Outlook');
    // Test link: Disney+ Hotstar
    await expect(page.locator('a:has-text("Disney+ Hotstar")')).toBeVisible();
    await expect(page.locator('a:has-text("Disney+ Hotstar")')).toContainText('Disney+ Hotstar');
    // Test link: Material UI
    await expect(page.locator('a:has-text("Material UI")')).toBeVisible();
    await expect(page.locator('a:has-text("Material UI")')).toContainText('Material UI');
    // Test link: ING
    await expect(page.locator('a:has-text("ING")')).toBeVisible();
    await expect(page.locator('a:has-text("ING")')).toContainText('ING');
    // Test link: Adobe
    await expect(page.locator('a:has-text("Adobe")')).toBeVisible();
    await expect(page.locator('a:has-text("Adobe")')).toContainText('Adobe');
    // Test link: React Navigation
    await expect(page.locator('a:has-text("React Navigation")')).toBeVisible();
    await expect(page.locator('a:has-text("React Navigation")')).toContainText('React Navigation');
    // Test link: Accessibility Insights
    await expect(page.locator('a:has-text("Accessibility Insights")')).toBeVisible();
    await expect(page.locator('a:has-text("Accessibility Insights")')).toContainText('Accessibility Insights');
    // Test link: Getting started
    await expect(page.locator('a:has-text("Getting started")')).toBeVisible();
    await expect(page.locator('a:has-text("Getting started")')).toContainText('Getting started');
    // Test link: Playwright Training
    await expect(page.locator('a:has-text("Playwright Training")')).toBeVisible();
    await expect(page.locator('a:has-text("Playwright Training")')).toContainText('Playwright Training');
    // Test link: Learn Videos
    await expect(page.locator('a:has-text("Learn Videos")')).toBeVisible();
    await expect(page.locator('a:has-text("Learn Videos")')).toContainText('Learn Videos');
    // Test link: Feature Videos
    await expect(page.locator('a:has-text("Feature Videos")')).toBeVisible();
    await expect(page.locator('a:has-text("Feature Videos")')).toContainText('Feature Videos');
    // Test link: Stack Overflow
    await expect(page.locator('a:has-text("Stack Overflow")')).toBeVisible();
    await expect(page.locator('a:has-text("Stack Overflow")')).toContainText('Stack Overflow');
    // Test link: Discord
    await expect(page.locator('a:has-text("Discord")')).toBeVisible();
    await expect(page.locator('a:has-text("Discord")')).toContainText('Discord');
    // Test link: Twitter
    await expect(page.locator('a:has-text("Twitter")')).toBeVisible();
    await expect(page.locator('a:has-text("Twitter")')).toContainText('Twitter');
    // Test link: LinkedIn
    await expect(page.locator('a:has-text("LinkedIn")')).toBeVisible();
    await expect(page.locator('a:has-text("LinkedIn")')).toContainText('LinkedIn');
    // Test link: GitHub
    await expect(page.locator('a:has-text("GitHub")')).toBeVisible();
    await expect(page.locator('a:has-text("GitHub")')).toContainText('GitHub');
    // Test link: YouTube
    await expect(page.locator('a:has-text("YouTube")')).toBeVisible();
    await expect(page.locator('a:has-text("YouTube")')).toContainText('YouTube');
    // Test link: Blog
    await expect(page.locator('a:has-text("Blog")')).toBeVisible();
    await expect(page.locator('a:has-text("Blog")')).toContainText('Blog');
    // Test link: Ambassadors
    await expect(page.locator('a:has-text("Ambassadors")')).toBeVisible();
    await expect(page.locator('a:has-text("Ambassadors")')).toContainText('Ambassadors');
  });

  test('Interactive buttons are present and clickable', async ({ page }) => {
    // Test button: Toggle navigation bar
    await expect(page.locator('button:has-text("Toggle navigation bar")')).toBeVisible();
    await expect(page.locator('button:has-text("Toggle navigation bar")')).toContainText('Toggle navigation bar');
    // Test button: Search (Ctrl+K)
    await expect(page.locator('button:has-text("Search (Ctrl+K)")')).toBeVisible();
    await expect(page.locator('button:has-text("Search (Ctrl+K)")')).toContainText('Search (Ctrl+K)');
  });

  test('Page content is present and readable', async ({ page }) => {
    // Test content: Browsers run web content belonging to different or...
    await expect(page.locator('p:has-text("Browsers run web con")')).toBeVisible();
  });

  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: `screenshots/${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png`,
        fullPage: true
      });
    }
  });
});
