import { test, expect } from '@playwright/test';

test.describe('Example Site Debug Test 3 - Exploration Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('https://example.com');
    await page.waitForLoadState('networkidle');
  });

  test('Page loads successfully with key elements', async ({ page }) => {
    // Verify page loads and basic elements are present
    await expect(page).toHaveTitle(/.+/);
    await expect(page.locator('body')).toBeVisible();

    // Take initial screenshot
    await page.screenshot({
      path: 'screenshots/example-site-debug-test-3-initial.png',
      fullPage: true
    });
  });


  test('Page headings are present and contain expected text', async ({ page }) => {
    // Test heading: Example Domain
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('h1')).toContainText('Example Domain');
  });

  test('Links are present and accessible', async ({ page }) => {
    // Test link: More information...
    await expect(page.locator('a:has-text("More information...")')).toBeVisible();
    await expect(page.locator('a:has-text("More information...")')).toContainText('More information...');
  });

  test('Page content is present and readable', async ({ page }) => {
    // Test content: This domain is for use in illustrative examples in...
    await expect(page.locator('p:has-text("This domain is for u")')).toBeVisible();
  });

  test.afterEach(async ({ page }) => {
    // Capture screenshot on failure
    if (test.info().status !== test.info().expectedStatus) {
      await page.screenshot({
        path: `screenshots/${test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png`,
        fullPage: true
      });
    }
  });
});
