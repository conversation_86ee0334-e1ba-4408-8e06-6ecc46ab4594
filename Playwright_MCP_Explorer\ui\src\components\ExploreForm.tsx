import React, { useState } from 'react';

interface ExploreFormProps {
  onGenerate: (url: string, testName: string) => void;
  isGenerating: boolean;
}

const ExploreForm: React.FC<ExploreFormProps> = ({ onGenerate, isGenerating }) => {
  const [url, setUrl] = useState('');
  const [testName, setTestName] = useState('');
  const [urlError, setUrlError] = useState('');

  const validateUrl = (value: string): boolean => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUrl(value);
    
    if (value && !validateUrl(value)) {
      setUrlError('Please enter a valid URL (e.g., https://example.com)');
    } else {
      setUrlError('');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url) {
      setUrlError('URL is required');
      return;
    }
    
    if (!validateUrl(url)) {
      setUrlError('Please enter a valid URL');
      return;
    }
    
    const finalTestName = testName.trim() || `Test for ${new URL(url).hostname}`;
    onGenerate(url, finalTestName);
  };

  const isFormValid = url && !urlError && !isGenerating;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
          Website URL *
        </label>
        <input
          type="url"
          id="url"
          value={url}
          onChange={handleUrlChange}
          placeholder="https://example.com"
          className={`input-field ${urlError ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
          disabled={isGenerating}
        />
        {urlError && (
          <p className="mt-1 text-sm text-red-600">{urlError}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="testName" className="block text-sm font-medium text-gray-700 mb-1">
          Test Name (optional)
        </label>
        <input
          type="text"
          id="testName"
          value={testName}
          onChange={(e) => setTestName(e.target.value)}
          placeholder="Homepage Test"
          className="input-field"
          disabled={isGenerating}
        />
        <p className="mt-1 text-xs text-gray-500">
          If empty, will auto-generate based on the URL
        </p>
      </div>
      
      <button
        type="submit"
        disabled={!isFormValid}
        className="btn-primary w-full flex items-center justify-center space-x-2"
      >
        {isGenerating ? (
          <>
            <div className="loading-spinner"></div>
            <span>Exploring Website...</span>
          </>
        ) : (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span>Generate Explore</span>
          </>
        )}
      </button>
    </form>
  );
};

export default ExploreForm;
