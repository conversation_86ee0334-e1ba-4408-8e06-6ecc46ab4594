import React, { useState } from 'react';

interface TestResultsProps {
  testContent: string | null;
  isGenerating: boolean;
}

const TestResults: React.FC<TestResultsProps> = ({ testContent, isGenerating }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    if (testContent) {
      try {
        await navigator.clipboard.writeText(testContent);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy text: ', err);
      }
    }
  };

  const handleDownload = () => {
    if (testContent) {
      const blob = new Blob([testContent], { type: 'text/typescript' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'generated-test.spec.ts';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  if (isGenerating) {
    return (
      <div className="card">
        <div className="flex items-center justify-center space-x-3 py-8">
          <div className="loading-spinner"></div>
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900">Generating Test...</h3>
            <p className="text-gray-600 mt-1">MCP is exploring the website and analyzing page structure</p>
          </div>
        </div>
      </div>
    );
  }

  if (!testContent) {
    return null;
  }

  return (
    <div className="card">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Generated Test</h3>
          <div className="flex space-x-2">
            <button
              onClick={handleCopy}
              className="btn-secondary flex items-center space-x-2"
            >
              {copied ? (
                <>
                  <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Copied!</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <span>Copy</span>
                </>
              )}
            </button>
            <button
              onClick={handleDownload}
              className="btn-primary flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Download</span>
            </button>
          </div>
        </div>
        
        <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre className="text-sm text-gray-100">
            <code>{testContent}</code>
          </pre>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-blue-900">Next Steps</h4>
              <p className="text-sm text-blue-700 mt-1">
                Save this test to your <code className="bg-blue-100 px-1 rounded">tests/generated/</code> directory and run it with:
              </p>
              <code className="block bg-blue-100 text-blue-800 px-2 py-1 rounded mt-2 text-xs">
                npx playwright test tests/generated/your-test.spec.ts
              </code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestResults;
