import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import ScenarioBuilder from '../components/ScenarioBuilder';
import TestResults from '../components/TestResults';

export interface VerifyStatement {
  id: string;
  text: string;
}

export interface Scenario {
  testName: string;
  url: string;
  description: string;
  verifyStatements: VerifyStatement[];
}

const Scenarios: React.FC = () => {
  const [scenario, setScenario] = useState<Scenario>({
    testName: '',
    url: '',
    description: '',
    verifyStatements: [{ id: '1', text: '' }]
  });
  const [generatedTest, setGeneratedTest] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleScenarioChange = (updatedScenario: Scenario) => {
    setScenario(updatedScenario);
  };

  const handleGenerate = async () => {
    setIsGenerating(true);
    setGeneratedTest(null);
    
    try {
      const response = await fetch('/api/scenarios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scenario: {
            id: scenario.testName.toLowerCase().replace(/\s+/g, '-'),
            name: scenario.testName,
            description: scenario.description,
            url: scenario.url,
            verifyStatements: scenario.verifyStatements.map(vs => vs.text).filter(text => text.trim())
          }
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate test');
      }
      
      const data = await response.json();
      setGeneratedTest(data.testContent);
    } catch (error) {
      console.error('Error generating test:', error);
      alert('Failed to generate test. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const isFormValid = scenario.testName.trim() && 
                     scenario.url.trim() && 
                     scenario.verifyStatements.some(vs => vs.text.trim()) &&
                     !isGenerating;

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link to="/" className="text-primary-600 hover:text-primary-700">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Build Test Scenario</h1>
          <p className="text-gray-600 mt-1">
            Write test requirements in natural language and generate working Playwright tests
          </p>
        </div>
      </div>

      {/* Scenario Builder */}
      <div className="card">
        <ScenarioBuilder
          scenario={scenario}
          onChange={handleScenarioChange}
          onGenerate={handleGenerate}
          isGenerating={isGenerating}
          isFormValid={isFormValid}
        />
      </div>

      {/* Results */}
      {(generatedTest || isGenerating) && (
        <TestResults 
          testContent={generatedTest}
          isGenerating={isGenerating}
        />
      )}
    </div>
  );
};

export default Scenarios;
