# TestSynth MCP Explorer - Web UI

A user-friendly web interface for generating intelligent Playwright tests using Model Context Protocol (MCP).

## Features

🎯 **Generate Explore** - Enter a URL and let MCP explore the website to create specific tests  
📝 **Generate From Scenario** - Write test requirements in natural language  
⚡ **Real-time Generation** - See your tests being created live  
📋 **Copy & Download** - Easy test export options  
🎨 **Modern UI** - Clean, responsive interface built with React + Tailwind CSS  

## Quick Start

### 1. Install Dependencies

```bash
# Install main project dependencies
npm install

# Install UI dependencies
npm run ui:install
```

### 2. Start the UI

```bash
# Start both backend API and React frontend
npm run ui
```

This will:
- Start the backend API server on `http://localhost:3001`
- Start the React frontend on `http://localhost:3000`
- Automatically open your browser

### 3. Generate Tests

#### Option A: Explore Website
1. Click "Generate Explore"
2. Enter a website URL (e.g., `https://example.com`)
3. Optionally enter a test name
4. Click "Generate Explore"
5. Watch as <PERSON><PERSON> explores the site and creates specific tests

#### Option B: Build Scenario
1. Click "Generate From Scenario" 
2. Fill in test details:
   - Test Name: "Login Flow Test"
   - URL: "https://myapp.com/login"
   - Verify Statements:
     - "Verify login form is visible"
     - "Verify email field accepts input"
     - "Verify password field is secure"
3. Click "Generate From Scenario"
4. Get working Playwright tests

## UI Structure

```
ui/
├── src/
│   ├── components/
│   │   ├── Header.tsx           # Navigation header
│   │   ├── ExploreForm.tsx      # URL exploration form
│   │   ├── ScenarioBuilder.tsx  # Scenario creation form
│   │   └── TestResults.tsx      # Generated test display
│   ├── pages/
│   │   ├── Home.tsx            # Main page with both options
│   │   └── Scenarios.tsx       # Scenario builder page
│   └── App.tsx                 # Main app component
├── server/
│   └── server.js               # Express API server
└── public/
    └── index.html              # HTML template
```

## API Endpoints

The backend provides these endpoints:

- `POST /api/explore` - Generate tests by exploring a website
- `POST /api/scenarios` - Generate tests from scenarios
- `GET /api/health` - Health check
- `GET /api/tests` - List generated tests

## Development

### Start in Development Mode

```bash
# Start with hot reload for both frontend and backend
npm run ui:dev
```

### Build for Production

```bash
# Build the React app
cd ui && npm run build
```

## How It Works

### Website Exploration
1. User enters URL in the web interface
2. Frontend sends request to `/api/explore`
3. Backend calls the MCP generation script
4. MCP server explores the website using a real browser
5. Generated test is returned and displayed in the UI

### Scenario Generation
1. User builds scenario with verify statements
2. Frontend sends scenario to `/api/scenarios`
3. Backend creates temporary scenario file
4. MCP processes the scenario and generates tests
5. Generated test is returned and displayed

## Troubleshooting

### UI Won't Start
```bash
# Make sure all dependencies are installed
npm install
npm run ui:install

# Check if ports 3000/3001 are available
netstat -an | findstr :3000
netstat -an | findstr :3001
```

### Backend API Errors
```bash
# Check the backend logs in the terminal
# Make sure the main project is built
npm run build

# Verify MCP is working
node scripts/generate-tests.js explore https://example.com "Test"
```

### Generated Tests Not Appearing
- Check that the `tests/generated/` directory exists
- Verify the MCP server is running correctly
- Check browser console for API errors

## Next Steps

After generating tests:

1. **Copy the test code** using the copy button
2. **Save to your project** in `tests/generated/your-test.spec.ts`
3. **Run the test**:
   ```bash
   npx playwright test tests/generated/your-test.spec.ts
   ```

The UI makes it easy to generate, preview, and export your intelligent Playwright tests!
