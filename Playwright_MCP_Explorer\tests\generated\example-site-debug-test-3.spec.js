"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const test_1 = require("@playwright/test");
test_1.test.describe('Example Site Debug Test 3 - Exploration Test', () => {
    test_1.test.beforeEach((_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        yield page.goto('https://example.com');
        yield page.waitForLoadState('networkidle');
    }));
    (0, test_1.test)('Page loads successfully with key elements', (_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Verify page loads and basic elements are present
        yield (0, test_1.expect)(page).toHaveTitle(/.+/);
        yield (0, test_1.expect)(page.locator('body')).toBeVisible();
        // Take initial screenshot
        yield page.screenshot({
            path: 'screenshots/example-site-debug-test-3-initial.png',
            fullPage: true
        });
    }));
    (0, test_1.test)('Page headings are present and contain expected text', (_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Test heading: Example Domain
        yield (0, test_1.expect)(page.locator('h1')).toBeVisible();
        yield (0, test_1.expect)(page.locator('h1')).toContainText('Example Domain');
    }));
    (0, test_1.test)('Links are present and accessible', (_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Test link: More information...
        yield (0, test_1.expect)(page.locator('a:has-text("More information...")')).toBeVisible();
        yield (0, test_1.expect)(page.locator('a:has-text("More information...")')).toContainText('More information...');
    }));
    (0, test_1.test)('Page content is present and readable', (_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Test content: This domain is for use in illustrative examples in...
        yield (0, test_1.expect)(page.locator('p:has-text("This domain is for u")')).toBeVisible();
    }));
    test_1.test.afterEach((_a) => __awaiter(void 0, [_a], void 0, function* ({ page }) {
        // Capture screenshot on failure
        if (test_1.test.info().status !== test_1.test.info().expectedStatus) {
            yield page.screenshot({
                path: `screenshots/${test_1.test.info().title.replace(/[^a-z0-9]/gi, '-')}-failure.png`,
                fullPage: true
            });
        }
    }));
});
