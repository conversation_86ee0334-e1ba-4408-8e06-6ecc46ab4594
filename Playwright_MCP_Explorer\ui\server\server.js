const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Path to the main project directory
const PROJECT_ROOT = path.join(__dirname, '..', '..');

// Helper function to execute the test generation script
const executeTestGeneration = (command, args) => {
  return new Promise((resolve, reject) => {
    const child = spawn('node', [path.join(PROJECT_ROOT, 'scripts', 'generate-tests.js'), ...args], {
      cwd: PROJECT_ROOT,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`Process exited with code ${code}: ${stderr}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
};

// Helper function to read generated test file
const readGeneratedTest = async (testName) => {
  const generatedDir = path.join(PROJECT_ROOT, 'tests', 'generated');
  const files = await fs.readdir(generatedDir);
  
  // Find the most recently created test file that matches the test name
  const testFiles = files.filter(file => 
    file.includes(testName.toLowerCase().replace(/\s+/g, '-')) && 
    file.endsWith('.spec.ts')
  );
  
  if (testFiles.length === 0) {
    throw new Error('Generated test file not found');
  }
  
  // Get the most recent file
  const testFile = testFiles[testFiles.length - 1];
  const testPath = path.join(generatedDir, testFile);
  const content = await fs.readFile(testPath, 'utf-8');
  
  return content;
};

// API Routes

// Explore endpoint - generates tests by exploring a website
app.post('/api/explore', async (req, res) => {
  try {
    const { url, testName } = req.body;
    
    if (!url || !testName) {
      return res.status(400).json({ 
        error: 'URL and test name are required' 
      });
    }

    console.log(`Generating test for URL: ${url}, Test Name: ${testName}`);
    
    // Execute the explore command
    await executeTestGeneration('explore', [url, testName]);
    
    // Read the generated test content
    const testContent = await readGeneratedTest(testName);
    
    res.json({
      success: true,
      testContent,
      message: 'Test generated successfully'
    });
    
  } catch (error) {
    console.error('Error in /api/explore:', error);
    res.status(500).json({
      error: 'Failed to generate test',
      details: error.message
    });
  }
});

// Scenarios endpoint - generates tests from scenarios
app.post('/api/scenarios', async (req, res) => {
  try {
    const { scenario } = req.body;
    
    if (!scenario || !scenario.name || !scenario.url || !scenario.verifyStatements) {
      return res.status(400).json({ 
        error: 'Scenario with name, URL, and verify statements is required' 
      });
    }

    console.log(`Generating test from scenario: ${scenario.name}`);
    
    // Update the scenarios file temporarily
    const scenariosPath = path.join(PROJECT_ROOT, 'test-data', 'scenarios.json');
    const backupPath = path.join(PROJECT_ROOT, 'test-data', 'scenarios.backup.json');

    // Backup existing scenarios
    try {
      const existingContent = await fs.readFile(scenariosPath, 'utf-8');
      await fs.writeFile(backupPath, existingContent);
    } catch (error) {
      // File might not exist, create empty backup
      await fs.writeFile(backupPath, JSON.stringify({ scenarios: [] }, null, 2));
    }

    // Write new scenario
    const scenarioData = {
      scenarios: [scenario]
    };

    await fs.writeFile(scenariosPath, JSON.stringify(scenarioData, null, 2));

    try {
      // Execute the scenarios command
      await executeTestGeneration('scenarios');

      // Read the generated test content
      const testContent = await readGeneratedTest(scenario.name);

      res.json({
        success: true,
        testContent,
        message: 'Test generated successfully from scenario'
      });

    } finally {
      // Restore original scenarios file
      try {
        const backupContent = await fs.readFile(backupPath, 'utf-8');
        await fs.writeFile(scenariosPath, backupContent);
        await fs.unlink(backupPath);
      } catch (cleanupError) {
        console.warn('Failed to restore scenarios file:', cleanupError);
      }
    }
    
  } catch (error) {
    console.error('Error in /api/scenarios:', error);
    res.status(500).json({
      error: 'Failed to generate test from scenario',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'TestSynth API Server is running',
    timestamp: new Date().toISOString()
  });
});

// List generated tests endpoint
app.get('/api/tests', async (req, res) => {
  try {
    const generatedDir = path.join(PROJECT_ROOT, 'tests', 'generated');
    const files = await fs.readdir(generatedDir);
    const testFiles = files.filter(file => file.endsWith('.spec.ts'));
    
    res.json({
      tests: testFiles,
      count: testFiles.length
    });
  } catch (error) {
    console.error('Error listing tests:', error);
    res.status(500).json({
      error: 'Failed to list tests',
      details: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`TestSynth API Server running on port ${PORT}`);
  console.log(`Project root: ${PROJECT_ROOT}`);
});
